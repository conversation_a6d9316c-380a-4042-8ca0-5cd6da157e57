using DynamicTableAPI.Data;
using DynamicTableAPI.Models;
using DynamicTableAPI.Validators;
using Microsoft.Data.SqlClient;
using System;
using System.Text;
using System.Text.Json;

namespace DynamicTableAPI.Services
{
    /// <summary>
    /// Service for managing dynamic table operations
    /// </summary>
    public class DynamicTableService : IDynamicTableService
    {
        private readonly DynamicTableDbContext _context;
        private readonly ITableValidationService _validationService;
        private readonly ILogger<DynamicTableService> _logger;

        public DynamicTableService(
            DynamicTableDbContext context,
            ITableValidationService validationService,
            ILogger<DynamicTableService> logger)
        {
            _context = context;
            _validationService = validationService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new dynamic table with the specified columns
        /// </summary>
        /// <param name="request">Table creation request</param>
        /// <returns>Created table schema</returns>
        public async Task<TableSchema> CreateTableAsync(CreateTableRequest request)
        {
            _logger.LogInformation("Creating table: {TableName}", request.TableName);

            // Validate the request
            var validationResult = _validationService.ValidateCreateTableRequest(request);
            if (!validationResult.IsValid)
            {
                throw new ArgumentException($"Validation failed: {string.Join(", ", validationResult.Errors)}");
            }

            // Check if table already exists
            if (await _context.TableExistsAsync(request.TableName))
            {
                throw new InvalidOperationException($"Table '{request.TableName}' already exists");
            }

            // Generate CREATE TABLE SQL
            var createTableSql = GenerateCreateTableSql(request);
            
            try
            {
                // Execute the CREATE TABLE command
                await _context.ExecuteRawSqlAsync(createTableSql);
                
                _logger.LogInformation("Successfully created table: {TableName}", request.TableName);

                // Return the created table schema
                return await GetTableSchemaAsync(request.TableName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table: {TableName}", request.TableName);
                throw new InvalidOperationException($"Failed to create table '{request.TableName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates a new table with metadata using the legacy AddFileIntoDB functionality
        /// </summary>
        /// <param name="request">Request containing table creation parameters</param>
        /// <returns>Response with creation result</returns>
        public async Task<AddFileToDbResponse> AddFileIntoDbAsync(AddFileToDbRequest request)
        {
            _logger.LogInformation("Creating table with metadata: {TableName}", request.TableName);

            try
            {
                // Ensure metadata table exists
                if (!await _context.EnsureMetadataTableExistsAsync())
                {
                    _logger.LogError("Failed to ensure metadata table exists");
                    return new AddFileToDbResponse
                    {
                        Success = false,
                        Message = "Failed to initialize metadata table"
                    };
                }

                // Validate the request using the validation service
                var validationResult = _validationService.ValidateAddFileToDbRequest(request);
                if (!validationResult.IsValid)
                {
                    _logger.LogWarning("AddFileToDb request validation failed for table: {TableName}. Errors: {Errors}",
                        request.TableName, string.Join(", ", validationResult.Errors));
                    return new AddFileToDbResponse
                    {
                        Success = false,
                        Message = "Validation failed",
                        Errors = validationResult.Errors
                    };
                }

                // Validate table name doesn't already exist
                if (await _context.TableExistsAsync(request.TableName))
                {
                    _logger.LogWarning("Attempted to create table that already exists: {TableName}", request.TableName);
                    return new AddFileToDbResponse
                    {
                        Success = false,
                        Message = "Table already exists"
                    };
                }

                // Validate table labels don't already exist
                if (await _context.TableLabelExistsAsync(request.TableLabel))
                {
                    _logger.LogWarning("Attempted to create table with existing label: {TableLabel}", request.TableLabel);
                    return new AddFileToDbResponse
                    {
                        Success = false,
                        Message = "Table label already exists"
                    };
                }

                if (await _context.TableLabelExistsAsync(request.TableLabelPlural))
                {
                    _logger.LogWarning("Attempted to create table with existing plural label: {TableLabelPlural}", request.TableLabelPlural);
                    return new AddFileToDbResponse
                    {
                        Success = false,
                        Message = "Table label plural already exists"
                    };
                }

                // Convert boolean parameters to integers for stored procedure
                int iCreateFileLabels = request.CreateFileLabels ? 1 : 0;
                int iCreateLinks = request.CreateLinks ? 1 : 0;
                int iCreateFieldLabels = request.CreateFieldLabels ? 1 : 0;
                int iCreateForm = request.CreateForm ? 1 : 0;
                int iCreateDesktop = request.CreateDesktop ? 1 : 0;
                int iCreatePermissions = request.CreatePermissions ? 1 : 0;

                // Build SQL query for stored procedure call
                string SqlQuery = "exec pCreateTable @par_sTableName='" + request.TableName +
                                 "',@par_sTableLabel='" + request.TableLabel +
                                 "',@par_sTableLabelPlural='" + request.TableLabelPlural +
                                 "',@par_bCreateFileLabels=" + iCreateFileLabels.ToString() +
                                 ",@par_bCreateLinks=" + iCreateLinks.ToString() +
                                 ",@par_bCreateFieldLabels=" + iCreateFieldLabels.ToString() +
                                 ",@par_bCreateForm=" + iCreateForm.ToString() +
                                 ",@par_bCreateDesktop=" + iCreateDesktop.ToString() +
                                 ",@par_bCreatePermissions=" + iCreatePermissions.ToString() +
                                 ",@par_bVerbose=0";

                // Execute the stored procedure
                bool result = await RunSQLQueryAsync(SqlQuery);

                if (!result)
                {
                    return new AddFileToDbResponse
                    {
                        Success = false,
                        Message = "Failed to create table using stored procedure"
                    };
                }

                // Save table metadata
                var metadata = new TableMetadata
                {
                    TableName = request.TableName,
                    TableLabel = request.TableLabel,
                    TableLabelPlural = request.TableLabelPlural,
                    CreateFileLabels = request.CreateFileLabels,
                    CreateLinks = request.CreateLinks,
                    CreateFieldLabels = request.CreateFieldLabels,
                    CreateForm = request.CreateForm,
                    CreateDesktop = request.CreateDesktop,
                    CreatePermissions = request.CreatePermissions,
                    CreatedDate = DateTime.UtcNow
                };

                await _context.SaveTableMetadataAsync(metadata);

                _logger.LogInformation("Successfully created table with metadata using stored procedure: {TableName}", request.TableName);

                // Try to get the table schema for the response (optional, since stored procedure may not create standard schema)
                TableSchema? tableSchema = null;
                try
                {
                    // Check if table exists and get its schema
                    if (await _context.TableExistsAsync(request.TableName))
                    {
                        tableSchema = await GetTableSchemaAsync(request.TableName);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not retrieve table schema after stored procedure execution for table: {TableName}", request.TableName);
                    // Continue without schema - the stored procedure succeeded
                }

                return new AddFileToDbResponse
                {
                    Success = true,
                    Message = "success",
                    TableSchema = tableSchema
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create table with metadata: {TableName}", request.TableName);
                return new AddFileToDbResponse
                {
                    Success = false,
                    Message = "Table Creation Failed: " + ex.Message
                };
            }
        }

        /// <summary>
        /// Legacy method for backward compatibility - converts to new async method
        /// </summary>
        [Obsolete("Use AddFileIntoDbAsync instead")]
        public string AddFileIntoDB(string TableName, string TableLabel, string TableLabelPlural, bool CreateFileLabels, bool CreateLinks, bool CreateFieldLabels, bool CreateForm, bool CreateDesktop, bool CreatePermissions)
        {
            var request = new AddFileToDbRequest
            {
                TableName = TableName,
                TableLabel = TableLabel,
                TableLabelPlural = TableLabelPlural,
                CreateFileLabels = CreateFileLabels,
                CreateLinks = CreateLinks,
                CreateFieldLabels = CreateFieldLabels,
                CreateForm = CreateForm,
                CreateDesktop = CreateDesktop,
                CreatePermissions = CreatePermissions
            };

            try
            {
                var result = AddFileIntoDbAsync(request).GetAwaiter().GetResult();
                return result.Success ? "success" : result.Message;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in legacy AddFileIntoDB method");
                return "Table Creation Failed";
            }
        }
        /// <summary>
        /// Gets list of tables with optional labels (modernized version of LoadSelltisTables)
        /// </summary>
        /// <param name="onlyTables">If true, returns only table names. If false, returns table names with labels</param>
        /// <returns>List of table names or table names with labels</returns>
        private async Task<List<string>> LoadSelltisTablesAsync(bool onlyTables = false)
        {
            try
            {
                var tables = new List<string>();
                var userTables = await _context.GetUserTablesAsync();

                foreach (var tableName in userTables)
                {
                    if (onlyTables)
                    {
                        tables.Add(tableName);
                    }
                    else
                    {
                        var metadata = await _context.GetTableMetadataAsync(tableName);
                        var label = metadata?.TableLabel ?? tableName;
                        tables.Add($"{tableName}|{label}");
                    }
                }

                return tables;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading Selltis tables");
                return new List<string>();
            }
        }

        /// <summary>
        /// Legacy method for backward compatibility
        /// </summary>
        [Obsolete("Use LoadSelltisTablesAsync instead")]
        private List<string> LoadSelltisTables(bool OnlyTables = false)
        {
            try
            {
                return LoadSelltisTablesAsync(OnlyTables).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in legacy LoadSelltisTables method");
                return new List<string>();
            }
        }

        /// <summary>
        /// Gets list of files/tables (modernized version of GetFiles)
        /// </summary>
        /// <param name="includeSysFiles">Whether to include system files</param>
        /// <returns>List of table names</returns>
        public async Task<List<string>> GetFilesAsync(bool includeSysFiles = true)
        {
            try
            {
                var tables = await _context.GetUserTablesAsync();

                if (!includeSysFiles)
                {
                    // Filter out system tables (tables starting with __ or sys)
                    tables = tables.Where(t => !IsFileSystem(t)).ToList();
                }

                return tables;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting files/tables");
                return new List<string>();
            }
        }

        /// <summary>
        /// Legacy method for backward compatibility
        /// </summary>
        [Obsolete("Use GetFilesAsync instead")]
        public List<string> GetFiles(bool par_bIncludeSysFiles = true)
        {
            try
            {
                return GetFilesAsync(par_bIncludeSysFiles).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in legacy GetFiles method");
                return new List<string>();
            }
        }

        /// <summary>
        /// Determines if a table name represents a system file/table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>True if it's a system table, false otherwise</returns>
        private bool IsFileSystem(string tableName)
        {
            if (string.IsNullOrEmpty(tableName))
                return false;

            // Consider tables starting with __ or sys as system tables
            return tableName.StartsWith("__", StringComparison.OrdinalIgnoreCase) ||
                   tableName.StartsWith("sys", StringComparison.OrdinalIgnoreCase) ||
                   tableName.StartsWith("INFORMATION_SCHEMA", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Executes a SQL query (modernized version of RunSQLQuery)
        /// </summary>
        /// <param name="query">SQL query to execute</param>
        /// <returns>True if successful, false otherwise</returns>
        public async Task<bool> RunSQLQueryAsync(string query)
        {
            const string methodName = "DynamicTableService::RunSQLQueryAsync";
            const int maxLogLength = 6000;

            string safeQuery = query.Length > maxLogLength
                ? query.Substring(0, maxLogLength) + "..."
                : query;

            _logger.LogDebug("{Method} - Executing query: {Query}", methodName, safeQuery);

            try
            {
                if (string.IsNullOrEmpty(query))
                {
                    _logger.LogDebug("{Method} - Empty query, returning true", methodName);
                    return true;
                }

                await _context.ExecuteRawSqlAsync(query);
                _logger.LogDebug("{Method} - Query executed successfully", methodName);
                return true;
            }
            catch (SqlException ex)
            {
                switch (ex.Number)
                {
                    case 2705: // Column names in each table must be unique
                    case 4924: // ALTER TABLE DROP COLUMN failed because column does not exist
                    case 2714: // There is already an object named 'xxx' in the database
                        _logger.LogWarning("{Method} - SQL Warning {ErrorNumber}: {Message}. Query: {Query}",
                            methodName, ex.Number, ex.Message, safeQuery);
                        return true; // Treat as warning, not error

                    case 512: // Subquery returned more than 1 value
                        _logger.LogError("{Method} - SQL Error {ErrorNumber}: Subquery returned more than 1 value. Query: {Query}",
                            methodName, ex.Number, safeQuery);
                        return false;

                    default:
                        _logger.LogError(ex, "{Method} - SQL Error {ErrorNumber}: {Message}. Query: {Query}",
                            methodName, ex.Number, ex.Message, safeQuery);
                        return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "{Method} - Error executing SQL query: {Message}. Query: {Query}",
                    methodName, ex.Message, safeQuery);
                return false;
            }
        }

        /// <summary>
        /// Legacy method for backward compatibility
        /// </summary>
        [Obsolete("Use RunSQLQueryAsync instead")]
        public bool RunSQLQuery(string par_sQuery)
        {
            try
            {
                return RunSQLQueryAsync(par_sQuery).GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in legacy RunSQLQuery method");
                return false;
            }
        }

        /// <summary>
        /// Gets the schema information for a specific table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <returns>Table schema information</returns>
        public async Task<TableSchema> GetTableSchemaAsync(string tableName)
        {
            _logger.LogInformation("Getting schema for table: {TableName}", tableName);

            if (!await _context.TableExistsAsync(tableName))
            {
                throw new ArgumentException($"Table '{tableName}' does not exist");
            }

            var schemaData = await _context.GetTableSchemaAsync(tableName);
            
            var tableSchema = new TableSchema
            {
                TableName = tableName,
                CreatedDate = DateTime.UtcNow, // This would ideally come from table metadata
                Columns = schemaData.Select(row => new ColumnSchema
                {
                    ColumnName = row["ColumnName"]?.ToString() ?? "",
                    DataType = row["DataType"]?.ToString() ?? "",
                    IsNullable = string.Equals(row["IsNullable"]?.ToString(), "YES", StringComparison.OrdinalIgnoreCase),
                    MaxLength = row["MaxLength"] as int?,
                    Precision = row["Precision"] as int?,
                    Scale = row["Scale"] as int?,
                    DefaultValue = row["DefaultValue"]?.ToString(),
                    IsPrimaryKey = Convert.ToBoolean(row["IsPrimaryKey"])
                }).ToList()
            };

            return tableSchema;
        }

        /// <summary>
        /// Gets a list of all user tables in the database
        /// </summary>
        /// <returns>List of table names</returns>
        public async Task<List<string>> GetTablesAsync()
        {
            _logger.LogInformation("Getting list of all tables");
            return await _context.GetUserTablesAsync();
        }

        /// <summary>
        /// Inserts data into a dynamic table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <param name="data">Data to insert</param>
        /// <returns>Number of rows affected</returns>
        public async Task<int> InsertDataAsync(string tableName, Dictionary<string, object?> data)
        {
            _logger.LogInformation("Inserting data into table: {TableName}", tableName);

            if (!await _context.TableExistsAsync(tableName))
            {
                throw new ArgumentException($"Table '{tableName}' does not exist");
            }

            if (data == null || !data.Any())
            {
                throw new ArgumentException("Data is required for insertion");
            }

            // Get table schema to validate columns
            var schema = await GetTableSchemaAsync(tableName);
            var validColumns = schema.Columns.Where(c => !c.IsPrimaryKey && 
                                                        !string.Equals(c.ColumnName, "CreatedDate", StringComparison.OrdinalIgnoreCase))
                                           .Select(c => c.ColumnName)
                                           .ToHashSet(StringComparer.OrdinalIgnoreCase);

            // Filter data to only include valid columns
            var filteredData = data.Where(kvp => validColumns.Contains(kvp.Key))
                                  .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            if (!filteredData.Any())
            {
                throw new ArgumentException("No valid columns found in the provided data");
            }

            // Add UpdatedDate if the column exists
            if (schema.Columns.Any(c => string.Equals(c.ColumnName, "UpdatedDate", StringComparison.OrdinalIgnoreCase)))
            {
                filteredData["UpdatedDate"] = DateTime.UtcNow;
            }

            // Generate INSERT SQL
            var insertSql = GenerateInsertSql(tableName, filteredData);

            try
            {
                var rowsAffected = await _context.ExecuteRawSqlAsync(insertSql);
                _logger.LogInformation("Successfully inserted {RowsAffected} rows into table: {TableName}", rowsAffected, tableName);
                return rowsAffected;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to insert data into table: {TableName}", tableName);
                throw new InvalidOperationException($"Failed to insert data into table '{tableName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves data from a dynamic table
        /// </summary>
        /// <param name="tableName">Name of the table</param>
        /// <param name="pageSize">Number of records per page (optional)</param>
        /// <param name="pageNumber">Page number (optional, 1-based)</param>
        /// <returns>List of data records</returns>
        public async Task<List<Dictionary<string, object?>>> GetDataAsync(string tableName, int? pageSize = null, int? pageNumber = null)
        {
            _logger.LogInformation("Getting data from table: {TableName}", tableName);

            if (!await _context.TableExistsAsync(tableName))
            {
                throw new ArgumentException($"Table '{tableName}' does not exist");
            }

            var sql = $"SELECT * FROM [{tableName}]";

            // Add pagination if specified
            if (pageSize.HasValue && pageNumber.HasValue)
            {
                var offset = (pageNumber.Value - 1) * pageSize.Value;
                sql += $" ORDER BY Id OFFSET {offset} ROWS FETCH NEXT {pageSize.Value} ROWS ONLY";
            }
            else
            {
                sql += " ORDER BY Id";
            }

            try
            {
                var result = await _context.ExecuteRawQueryAsync(sql);
                _logger.LogInformation("Successfully retrieved {RecordCount} records from table: {TableName}", result.Count, tableName);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve data from table: {TableName}", tableName);
                throw new InvalidOperationException($"Failed to retrieve data from table '{tableName}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Generates CREATE TABLE SQL statement
        /// </summary>
        /// <param name="request">Table creation request</param>
        /// <returns>CREATE TABLE SQL statement</returns>
        private string GenerateCreateTableSql(CreateTableRequest request)
        {
            var sql = new StringBuilder();
            sql.AppendLine($"CREATE TABLE [{request.TableName}] (");

            // Add default columns
            sql.AppendLine("    [Id] int IDENTITY(1,1) PRIMARY KEY,");
            sql.AppendLine("    [CreatedDate] datetime2 NOT NULL DEFAULT GETUTCDATE(),");
            sql.AppendLine("    [UpdatedDate] datetime2 NULL,");

            // Add custom columns
            foreach (var column in request.Columns)
            {
                sql.AppendLine($"    {GenerateColumnDefinitionSql(column)},");
            }

            // Remove the last comma and close the table definition
            var sqlString = sql.ToString().TrimEnd();
            sqlString = sqlString.TrimEnd(',');
            sqlString += Environment.NewLine + ");";

            return sqlString;
        }

        /// <summary>
        /// Generates SQL column definition
        /// </summary>
        /// <param name="column">Column definition</param>
        /// <returns>SQL column definition</returns>
        private string GenerateColumnDefinitionSql(ColumnDefinition column)
        {
            var sql = new StringBuilder();
            sql.Append($"[{column.Name}] ");

            // Map data types to SQL Server types
            switch (column.Type.ToLower())
            {
                case "string":
                    var maxLength = column.MaxLength ?? 255;
                    sql.Append($"nvarchar({maxLength})");
                    break;
                case "int":
                    sql.Append("int");
                    break;
                case "datetime":
                    sql.Append("datetime2");
                    break;
                case "bool":
                    sql.Append("bit");
                    break;
                case "decimal":
                    var precision = column.Precision ?? 18;
                    var scale = column.Scale ?? 2;
                    sql.Append($"decimal({precision},{scale})");
                    break;
                case "guid":
                    sql.Append("uniqueidentifier");
                    break;
                default:
                    throw new ArgumentException($"Unsupported data type '{column.Type}' for column '{column.Name}'");
            }

            // Add NULL/NOT NULL constraint
            sql.Append(column.IsRequired ? " NOT NULL" : " NULL");

            // Add default value if specified
            if (column.DefaultValue != null)
            {
                sql.Append($" DEFAULT {FormatDefaultValue(column.DefaultValue, column.Type)}");
            }

            return sql.ToString();
        }

        /// <summary>
        /// Formats default value for SQL
        /// </summary>
        /// <param name="value">Default value</param>
        /// <param name="dataType">Data type</param>
        /// <returns>Formatted default value</returns>
        private string FormatDefaultValue(object value, string dataType)
        {
            try
            {
                // Handle JsonElement objects from JSON deserialization
                if (value is System.Text.Json.JsonElement jsonElement)
                {
                    value = ConvertJsonElementToNativeType(jsonElement, dataType);
                }

                switch (dataType.ToLower())
                {
                    case "string":
                        return $"'{value.ToString()?.Replace("'", "''")}'";
                    case "bool":
                        return Convert.ToBoolean(value) ? "1" : "0";
                    case "datetime":
                        if (value.ToString()?.ToUpper() == "GETUTCDATE()")
                            return "GETUTCDATE()";
                        return $"'{value}'";
                    case "guid":
                        return $"'{value}'";
                    case "int":
                        return Convert.ToInt32(value).ToString();
                    case "decimal":
                        return Convert.ToDecimal(value).ToString();
                    default:
                        return value.ToString() ?? "NULL";
                }
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"Failed to format default value '{value}' for data type '{dataType}': {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Converts JsonElement to native .NET type based on the specified data type
        /// </summary>
        /// <param name="jsonElement">JsonElement to convert</param>
        /// <param name="dataType">Target data type</param>
        /// <returns>Converted value</returns>
        private object ConvertJsonElementToNativeType(System.Text.Json.JsonElement jsonElement, string dataType)
        {
            switch (dataType.ToLower())
            {
                case "string":
                    return jsonElement.GetString() ?? "";
                case "bool":
                    return jsonElement.GetBoolean();
                case "int":
                    return jsonElement.GetInt32();
                case "decimal":
                    return jsonElement.GetDecimal();
                case "datetime":
                    return jsonElement.GetDateTime();
                case "guid":
                    return jsonElement.GetGuid();
                default:
                    return jsonElement.ToString();
            }
        }

        /// <summary>
        /// Generates INSERT SQL statement
        /// </summary>
        /// <param name="tableName">Table name</param>
        /// <param name="data">Data to insert</param>
        /// <returns>INSERT SQL statement</returns>
        private string GenerateInsertSql(string tableName, Dictionary<string, object?> data)
        {
            var columns = string.Join(", ", data.Keys.Select(k => $"[{k}]"));
            var values = string.Join(", ", data.Values.Select(FormatValueForSql));

            return $"INSERT INTO [{tableName}] ({columns}) VALUES ({values})";
        }

        /// <summary>
        /// Formats a value for SQL insertion
        /// </summary>
        /// <param name="value">Value to format</param>
        /// <returns>Formatted SQL value</returns>
        
        private string FormatValueForSql(object? value)
        {
            if (value == null)
                return "NULL";

            // Handle System.Text.Json.JsonElement
            if (value is JsonElement je)
            {
                return je.ValueKind switch
                {
                    JsonValueKind.String => $"'{je.GetString()?.Replace("'", "''")}'",
                    JsonValueKind.Number => je.ToString(),
                    JsonValueKind.True => "1",
                    JsonValueKind.False => "0",
                    JsonValueKind.Null => "NULL",
                    _ => $"'{je.ToString()?.Replace("'", "''")}'"
                };
            }

            // Handle regular CLR types
            switch (value)
            {
                case string s:
                    return $"'{s.Replace("'", "''")}'";
                case DateTime dt:
                    return $"'{dt:yyyy-MM-dd HH:mm:ss.fff}'";
                case bool b:
                    return b ? "1" : "0";
                case Guid g:
                    return $"'{g}'";
                case Enum e:
                    return Convert.ToInt32(e).ToString();
                default:
                    return value.ToString()?.ToLower() == "true" ? "1" :
                           value.ToString()?.ToLower() == "false" ? "0" :
                           $"'{value.ToString()?.Replace("'", "''")}'";
            }
        }
    }

    /// <summary>
    /// Interface for dynamic table service
    /// </summary>
    public interface IDynamicTableService
    {
        Task<TableSchema> CreateTableAsync(CreateTableRequest request);
        Task<TableSchema> GetTableSchemaAsync(string tableName);
        Task<List<string>> GetTablesAsync();
        Task<int> InsertDataAsync(string tableName, Dictionary<string, object?> data);
        Task<List<Dictionary<string, object?>>> GetDataAsync(string tableName, int? pageSize = null, int? pageNumber = null);
        Task<AddFileToDbResponse> AddFileIntoDbAsync(AddFileToDbRequest request);
        [Obsolete("Use AddFileIntoDbAsync instead")]
        string AddFileIntoDB(string TableName, string TableLabel, string TableLabelPlural, bool CreateFileLabels, bool CreateLinks, bool CreateFieldLabels, bool CreateForm, bool CreateDesktop, bool CreatePermissions);
    }
}
